<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/backgroundColor"
    tools:context=".features.emoji.presentation.gallery.EmojiBatteryFragment">

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Feature Toggle Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/featureToggleCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:strokeColor="?attr/colorOutline"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/featureTitle"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/emoji_battery_feature"
                                android:textAppearance="?attr/textAppearanceHeadlineSmall"
                                android:textColor="?attr/colorOnSurface" />

                            <TextView
                                android:id="@+id/featureDescription"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/emoji_battery_description"
                                android:textAppearance="?attr/textAppearanceBodyMedium"
                                android:textColor="?attr/colorOnSurfaceVariant" />

                        </LinearLayout>

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/featureToggleSwitch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp" />

                    </LinearLayout>

                    <!-- Permission Status -->
                    <LinearLayout
                        android:id="@+id/permissionStatusLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:orientation="horizontal"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <ImageView
                            android:id="@+id/permissionStatusIcon"
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_warning"
                            app:tint="?attr/colorError" />

                        <TextView
                            android:id="@+id/permissionStatusText"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:text="@string/permissions_required"
                            android:textAppearance="?attr/textAppearanceBodySmall"
                            android:textColor="?attr/colorError" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/requestPermissionsButton"
                            style="@style/Widget.Material3.Button.TextButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/grant_permissions"
                            android:textSize="12sp" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Filter and Search Section -->
            <LinearLayout
                android:id="@+id/filterSection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:orientation="vertical">

                <!-- Filter Chips -->
                <com.google.android.material.chip.ChipGroup
                    android:id="@+id/filterChipGroup"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    app:chipSpacingHorizontal="8dp"
                    app:singleSelection="false">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipAll"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        android:text="@string/all_styles" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipPopular"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/popular" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipFree"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/free" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipPremium"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/premium" />

                </com.google.android.material.chip.ChipGroup>

                <!-- Search Bar -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/searchInputLayout"
                    style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/search_styles"
                    app:endIconDrawable="@drawable/ic_search"
                    app:endIconMode="custom"
                    app:startIconDrawable="@drawable/ic_search">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/searchEditText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:imeOptions="actionSearch"
                        android:inputType="text"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

            <!-- Category Tabs (Horizontal ScrollView) -->
            <HorizontalScrollView
                android:id="@+id/categoryScrollView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:scrollbars="none">

                <com.google.android.material.chip.ChipGroup
                    android:id="@+id/categoryChipGroup"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:chipSpacingHorizontal="8dp"
                    app:singleLine="true"
                    app:singleSelection="true">

                    <!-- Category chips will be added programmatically -->

                </com.google.android.material.chip.ChipGroup>

            </HorizontalScrollView>

            <!-- Styles Grid -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/stylesRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                tools:itemCount="6"
                tools:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                tools:listitem="@layout/item_battery_style"
                tools:spanCount="2" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Loading Overlay -->
    <FrameLayout
        android:id="@+id/loadingOverlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?attr/backgroundColor"
        android:visibility="gone"
        tools:visibility="visible">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:orientation="vertical">

            <com.google.android.material.progressindicator.CircularProgressIndicator
                android:id="@+id/loadingProgressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:indeterminate="true" />

            <TextView
                android:id="@+id/loadingText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/loading_styles"
                android:textAppearance="?attr/textAppearanceBodyMedium"
                android:textColor="?attr/colorOnSurface" />

        </LinearLayout>

    </FrameLayout>

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/emptyStateLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="32dp"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/emptyStateIcon"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:src="@drawable/ic_emoji_battery"
            app:tint="?attr/colorOnSurfaceVariant" />

        <TextView
            android:id="@+id/emptyStateTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/no_styles_found"
            android:textAppearance="?attr/textAppearanceHeadlineSmall"
            android:textColor="?attr/colorOnSurface" />

        <TextView
            android:id="@+id/emptyStateMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:text="@string/no_styles_message"
            android:textAppearance="?attr/textAppearanceBodyMedium"
            android:textColor="?attr/colorOnSurfaceVariant" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/retryButton"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/retry" />

    </LinearLayout>

    <!-- Error Snackbar will be shown programmatically -->

    <!-- Pull to Refresh -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- This will wrap the NestedScrollView for pull-to-refresh functionality -->

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
